<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - StageMinder Admin Console</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.css" rel="stylesheet">
    <link rel="icon" href="data:,">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar Navigation -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <img src="../assets/images/logo.webp" alt="StageMinder Logo" class="logo-img" />
                    <span class="logo-text">StageMinder</span>
                </div>
                <button class="sidebar-toggle" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            
            <nav class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">Main</div>
                    <ul class="nav-list">
                        <li class="nav-item active">
                            <a href="dashboard.html" class="nav-link">
                                <i class="fas fa-tachometer-alt"></i>
                                <span>Dashboard</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="users.html" class="nav-link">
                                <i class="fas fa-users"></i>
                                <span>User Management</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="acts-venues.html" class="nav-link">
                                <i class="fas fa-music"></i>
                                <span>Acts & Venues</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="analytics.html" class="nav-link">
                                <i class="fas fa-chart-line"></i>
                                <span>Analytics</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="reports.html" class="nav-link">
                                <i class="fas fa-file-alt"></i>
                                <span>Reports</span>
                            </a>
                        </li>
                    </ul>
                </div>
                
                <div class="nav-section">
                    <div class="nav-section-title">System</div>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="#settings" class="nav-link" onclick="showSection('settings')">
                                <i class="fas fa-cog"></i>
                                <span>Settings</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#logs" class="nav-link" onclick="showSection('logs')">
                                <i class="fas fa-clipboard-list"></i>
                                <span>System Logs</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
            
            <div class="sidebar-footer">
                <div class="connection-status">
                    <span id="connectionStatus" class="status disconnected">
                        <i class="fas fa-circle"></i>
                        <span>Disconnected</span>
                    </span>
                </div>
            </div>
        </aside>

        <!-- Main Content Area -->
        <div class="main-wrapper">
            <!-- Top Navigation -->
            <header class="top-nav">
                <div class="breadcrumb">
                    <span class="breadcrumb-item">
                        <i class="fas fa-home"></i>
                        Admin Console
                    </span>
                    <span class="breadcrumb-separator">/</span>
                    <span class="breadcrumb-item" id="currentSection">Dashboard</span>
                </div>
                
                <div class="top-nav-actions">
                    <button class="action-btn" onclick="refreshData()" title="Refresh Data">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                    <button class="action-btn" onclick="showNotifications()" title="Notifications">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge">3</span>
                    </button>
                    <div class="user-menu">
                        <button class="user-avatar" onclick="toggleUserMenu()">
                            <i class="fas fa-user-circle"></i>
                            <span>Admin User</span>
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="user-dropdown" id="userDropdown">
                            <a href="#profile" class="dropdown-item">
                                <i class="fas fa-user"></i>
                                Profile
                            </a>
                            <a href="#logout" class="dropdown-item">
                                <i class="fas fa-sign-out-alt"></i>
                                Logout
                            </a>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Main Content -->
            <main class="main-content">
                <!-- Dashboard Section -->
                <div class="page-header">
                    <h1 class="page-title">Dashboard Overview</h1>
                    <p class="page-description">System status and key metrics at a glance</p>
                </div>
                
                <!-- Statistics Cards -->
                <div class="stats-grid">
                    <div class="stat-card primary">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number" id="totalUsersCount">0</div>
                            <div class="stat-label">Total Users</div>
                            <div class="stat-change positive">
                                <i class="fas fa-arrow-up"></i>
                                12% from last month
                            </div>
                        </div>
                    </div>
                    
                    <div class="stat-card success">
                        <div class="stat-icon">
                            <i class="fas fa-user-check"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number" id="activeUsersCount">0</div>
                            <div class="stat-label">Active Users</div>
                            <div class="stat-change positive">
                                <i class="fas fa-arrow-up"></i>
                                8% from last month
                            </div>
                        </div>
                    </div>
                    
                    <div class="stat-card warning">
                        <div class="stat-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number" id="secureUsersCount">0</div>
                            <div class="stat-label">2FA Enabled</div>
                            <div class="stat-change positive">
                                <i class="fas fa-arrow-up"></i>
                                15% from last month
                            </div>
                        </div>
                    </div>
                    
                    <div class="stat-card info">
                        <div class="stat-icon">
                            <i class="fas fa-database"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number" id="systemHealthScore">98%</div>
                            <div class="stat-label">System Health</div>
                            <div class="stat-change positive">
                                <i class="fas fa-arrow-up"></i>
                                Excellent
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="dashboard-section">
                    <h2 class="section-title">Quick Actions</h2>
                    <div class="quick-actions">
                        <button class="action-card" onclick="window.location.href='users.html'">
                            <i class="fas fa-users"></i>
                            <span>View All Users</span>
                            <div class="action-description">Access complete user database</div>
                        </button>
                        <button class="action-card" onclick="window.location.href='reports.html'">
                            <i class="fas fa-chart-bar"></i>
                            <span>Generate Report</span>
                            <div class="action-description">Create comprehensive statistics</div>
                        </button>
                        <button class="action-card" onclick="checkSystemHealth()">
                            <i class="fas fa-heartbeat"></i>
                            <span>System Check</span>
                            <div class="action-description">Run diagnostic tests</div>
                        </button>
                        <button class="action-card" onclick="window.location.href='analytics.html'">
                            <i class="fas fa-cog"></i>
                            <span>Analytics</span>
                            <div class="action-description">View detailed analytics</div>
                        </button>
                    </div>
                </div>

                <!-- Dashboard Widgets -->
                <div class="dashboard-widgets-grid">
                    <div class="widget-card">
                        <div class="widget-title"><i class="fas fa-chart-line"></i> Daily Active Users (30d)</div>
                        <canvas id="dauChart" height="120"></canvas>
                    </div>
                    <div class="widget-card">
                        <div class="widget-title"><i class="fas fa-share-nodes"></i> Sign-in Providers</div>
                        <canvas id="providersChartDashboard" height="120"></canvas>
                    </div>
                    <div class="widget-card">
                        <div class="widget-title"><i class="fas fa-star"></i> Top Artists</div>
                        <div class="results-container">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>User</th>
                                        <th>Followers</th>
                                        <th>Posts</th>
                                        <th>Last Login</th>
                                    </tr>
                                </thead>
                                <tbody id="topArtistsBody"></tbody>
                            </table>
                        </div>
                    </div>
                    <div class="widget-card">
                        <div class="widget-title"><i class="fas fa-shield-alt"></i> Security Overview</div>
                        <div class="progress-group">
                            <div class="progress-row">
                                <div class="progress-label">2FA Adoption</div>
                                <div class="progress"><div id="prog2fa" class="progress-bar" style="width: 0%"></div></div>
                                <div id="prog2faPct" class="progress-pct">0%</div>
                            </div>
                            <div class="progress-row">
                                <div class="progress-label">Active Users</div>
                                <div class="progress"><div id="progActive" class="progress-bar success" style="width: 0%"></div></div>
                                <div id="progActivePct" class="progress-pct">0%</div>
                            </div>
                            <div class="progress-row">
                                <div class="progress-label">Social Login Ratio</div>
                                <div class="progress"><div id="progSocial" class="progress-bar info" style="width: 0%"></div></div>
                                <div id="progSocialPct" class="progress-pct">0%</div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Chart.js library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <!-- Common Script -->
    <script src="../assets/js/common.js"></script>
    <!-- Dashboard Script -->
    <script src="../assets/js/dashboard.js"></script>
</body>
</html>
