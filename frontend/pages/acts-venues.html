<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Acts & Venues - StageMinder Admin Console</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.css" rel="stylesheet">
    <link rel="icon" href="data:,">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar Navigation -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <img src="../assets/images/logo.webp" alt="StageMinder Logo" class="logo-img" />
                    <span class="logo-text">StageMinder</span>
                </div>
                <button class="sidebar-toggle" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            
            <nav class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">Main</div>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="dashboard.html" class="nav-link">
                                <i class="fas fa-tachometer-alt"></i>
                                <span>Dashboard</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="users.html" class="nav-link">
                                <i class="fas fa-users"></i>
                                <span>User Management</span>
                            </a>
                        </li>
                        <li class="nav-item active">
                            <a href="acts-venues.html" class="nav-link">
                                <i class="fas fa-music"></i>
                                <span>Acts & Venues</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="analytics.html" class="nav-link">
                                <i class="fas fa-chart-line"></i>
                                <span>Analytics</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="reports.html" class="nav-link">
                                <i class="fas fa-file-alt"></i>
                                <span>Reports</span>
                            </a>
                        </li>
                    </ul>
                </div>
                
                <div class="nav-section">
                    <div class="nav-section-title">System</div>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="#settings" class="nav-link" onclick="showSection('settings')">
                                <i class="fas fa-cog"></i>
                                <span>Settings</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#logs" class="nav-link" onclick="showSection('logs')">
                                <i class="fas fa-clipboard-list"></i>
                                <span>System Logs</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
            
            <div class="sidebar-footer">
                <div class="connection-status">
                    <span id="connectionStatus" class="status disconnected">
                        <i class="fas fa-circle"></i>
                        <span>Disconnected</span>
                    </span>
                </div>
            </div>
        </aside>

        <!-- Main Content Area -->
        <div class="main-wrapper">
            <!-- Top Navigation -->
            <header class="top-nav">
                <div class="breadcrumb">
                    <span class="breadcrumb-item">
                        <i class="fas fa-home"></i>
                        Admin Console
                    </span>
                    <span class="breadcrumb-separator">/</span>
                    <span class="breadcrumb-item" id="currentSection">Acts & Venues</span>
                </div>
                
                <div class="top-nav-actions">
                    <button class="action-btn" onclick="refreshData()" title="Refresh Data">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                    <button class="action-btn" onclick="showNotifications()" title="Notifications">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge">3</span>
                    </button>
                    <div class="user-menu">
                        <button class="user-avatar" onclick="toggleUserMenu()">
                            <i class="fas fa-user-circle"></i>
                            <span>Admin User</span>
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="user-dropdown" id="userDropdown">
                            <a href="#profile" class="dropdown-item">
                                <i class="fas fa-user"></i>
                                Profile
                            </a>
                            <a href="#logout" class="dropdown-item">
                                <i class="fas fa-sign-out-alt"></i>
                                Logout
                            </a>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Main Content -->
            <main class="main-content">
                <!-- Acts & Venues Section -->
                <div class="page-header">
                    <h1 class="page-title">Acts & Venues Management</h1>
                    <p class="page-description">Manage acts and venues data</p>
                </div>
                
                <!-- Statistics Cards -->
                <div class="stats-grid">
                    <div class="stat-card primary">
                        <div class="stat-icon">
                            <i class="fas fa-music"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number" id="totalActsCount">0</div>
                            <div class="stat-label">Total Acts</div>
                            <div class="stat-change positive">
                                <i class="fas fa-arrow-up"></i>
                                Including virtual acts
                            </div>
                        </div>
                    </div>
                    
                    <div class="stat-card success">
                        <div class="stat-icon">
                            <i class="fas fa-building"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number" id="totalVenuesCount">0</div>
                            <div class="stat-label">Total Venues</div>
                            <div class="stat-change positive">
                                <i class="fas fa-arrow-up"></i>
                                Including virtual venues
                            </div>
                        </div>
                    </div>
                    
                    <div class="stat-card warning">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number" id="publishedActsCount">0</div>
                            <div class="stat-label">Published Acts</div>
                            <div class="stat-change positive">
                                <i class="fas fa-arrow-up"></i>
                                Active profiles
                            </div>
                        </div>
                    </div>
                    
                    <div class="stat-card info">
                        <div class="stat-icon">
                            <i class="fas fa-globe"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number" id="totalProfilesCount">0</div>
                            <div class="stat-label">Total Profiles</div>
                            <div class="stat-change positive">
                                <i class="fas fa-arrow-up"></i>
                                All profile types
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="dashboard-section">
                    <h2 class="section-title">Quick Actions</h2>
                    <div class="quick-actions">
                        <button class="action-card" onclick="refreshActsVenuesStats()">
                            <i class="fas fa-sync-alt"></i>
                            <span>Refresh Statistics</span>
                            <div class="action-description">Update acts and venues counts</div>
                        </button>
                        <button class="action-card" onclick="viewAllActs()">
                            <i class="fas fa-list"></i>
                            <span>View All Acts</span>
                            <div class="action-description">Display all act profiles</div>
                        </button>
                        <button class="action-card" onclick="viewAllVenues()">
                            <i class="fas fa-building"></i>
                            <span>View All Venues</span>
                            <div class="action-description">Display all venue profiles</div>
                        </button>
                    </div>
                </div>

                <!-- Filter Panel -->
                <div class="filter-panel">
                    <h3 class="filter-title">View Filters</h3>
                    <div class="filter-buttons">
                        <button class="filter-btn active" onclick="viewActsVenuesStats()" data-filter="stats">
                            <i class="fas fa-chart-bar"></i>
                            Statistics Overview
                        </button>
                        <button class="filter-btn" onclick="viewAllActs()" data-filter="acts">
                            <i class="fas fa-music"></i>
                            All Acts
                        </button>
                        <button class="filter-btn" onclick="viewAllVenues()" data-filter="venues">
                            <i class="fas fa-building"></i>
                            All Venues
                        </button>
                    </div>
                </div>
                
                <!-- Results Section -->
                <div class="results-section">
                    <div class="results-header">
                        <h3 id="actsVenuesResultsTitle">Statistics Overview</h3>
                        <div class="results-info">
                            <span id="actsVenuesResultsCount">No results</span>
                            <span id="actsVenuesQueryTime"></span>
                        </div>
                    </div>
                    
                    <div class="loading" id="actsVenuesLoading" style="display: none;">
                        <i class="fas fa-spinner fa-spin"></i>
                        <span>Loading data...</span>
                    </div>
                    
                    <div class="error-message" id="actsVenuesErrorMessage" style="display: none;">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span id="actsVenuesErrorText"></span>
                    </div>
                    
                    <div class="results-container">
                        <div id="actsVenuesResultsTable" class="results-table">
                            <div class="no-results">
                                <i class="fas fa-music"></i>
                                <p>Select a filter above to view acts and venues data</p>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Chart.js library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <!-- Common Script -->
    <script src="../assets/js/common.js"></script>
    <!-- Acts & Venues Script -->
    <script src="../assets/js/acts-venues.js"></script>
</body>
</html>
