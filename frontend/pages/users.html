<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management - StageMinder Admin Console</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.css" rel="stylesheet">
    <link rel="icon" href="data:,">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar Navigation -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <img src="../assets/images/logo.webp" alt="StageMinder Logo" class="logo-img" />
                    <span class="logo-text">StageMinder</span>
                </div>
                <button class="sidebar-toggle" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            
            <nav class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">Main</div>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="dashboard.html" class="nav-link">
                                <i class="fas fa-tachometer-alt"></i>
                                <span>Dashboard</span>
                            </a>
                        </li>
                        <li class="nav-item active">
                            <a href="users.html" class="nav-link">
                                <i class="fas fa-users"></i>
                                <span>User Management</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="acts-venues.html" class="nav-link">
                                <i class="fas fa-music"></i>
                                <span>Acts & Venues</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="analytics.html" class="nav-link">
                                <i class="fas fa-chart-line"></i>
                                <span>Analytics</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="reports.html" class="nav-link">
                                <i class="fas fa-file-alt"></i>
                                <span>Reports</span>
                            </a>
                        </li>
                    </ul>
                </div>
                
                <div class="nav-section">
                    <div class="nav-section-title">System</div>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="#settings" class="nav-link" onclick="showSection('settings')">
                                <i class="fas fa-cog"></i>
                                <span>Settings</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#logs" class="nav-link" onclick="showSection('logs')">
                                <i class="fas fa-clipboard-list"></i>
                                <span>System Logs</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
            
            <div class="sidebar-footer">
                <div class="connection-status">
                    <span id="connectionStatus" class="status disconnected">
                        <i class="fas fa-circle"></i>
                        <span>Disconnected</span>
                    </span>
                </div>
            </div>
        </aside>

        <!-- Main Content Area -->
        <div class="main-wrapper">
            <!-- Top Navigation -->
            <header class="top-nav">
                <div class="breadcrumb">
                    <span class="breadcrumb-item">
                        <i class="fas fa-home"></i>
                        Admin Console
                    </span>
                    <span class="breadcrumb-separator">/</span>
                    <span class="breadcrumb-item" id="currentSection">User Management</span>
                </div>
                
                <div class="top-nav-actions">
                    <button class="action-btn" onclick="refreshData()" title="Refresh Data">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                    <button class="action-btn" onclick="showNotifications()" title="Notifications">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge">3</span>
                    </button>
                    <div class="user-menu">
                        <button class="user-avatar" onclick="toggleUserMenu()">
                            <i class="fas fa-user-circle"></i>
                            <span>Admin User</span>
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="user-dropdown" id="userDropdown">
                            <a href="#profile" class="dropdown-item">
                                <i class="fas fa-user"></i>
                                Profile
                            </a>
                            <a href="#logout" class="dropdown-item">
                                <i class="fas fa-sign-out-alt"></i>
                                Logout
                            </a>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Main Content -->
            <main class="main-content">
                <!-- User Management Section -->
                <div class="page-header">
                    <h1 class="page-title">User Management</h1>
                    <p class="page-description">Manage users, permissions, and access controls</p>
                </div>
                
                <!-- Search and Filters -->
                <div class="toolbar">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" placeholder="Search users..." id="userSearch">
                    </div>
                    <div class="toolbar-actions">
                        <button class="btn secondary" onclick="exportUsers()">
                            <i class="fas fa-download"></i>
                            Export
                        </button>
                        <button class="btn primary" onclick="addUser()">
                            <i class="fas fa-plus"></i>
                            Add User
                        </button>
                    </div>
                </div>
                
                <!-- Query Filters -->
                <div class="filter-panel">
                    <h3 class="filter-title">Query Filters</h3>
                    <div class="filter-buttons">
                        <button class="filter-btn active" onclick="queryAllUsers()" data-filter="all">
                            <i class="fas fa-users"></i>
                            All Users
                        </button>
                        <button class="filter-btn" onclick="queryEnabledUsers()" data-filter="enabled">
                            <i class="fas fa-user-check"></i>
                            Active Only
                        </button>
                        <button class="filter-btn" onclick="queryArtistUsers()" data-filter="artists">
                            <i class="fas fa-palette"></i>
                            Artists
                        </button>
                        <button class="filter-btn" onclick="queryUsersWithLocation()" data-filter="location">
                            <i class="fas fa-map-marker-alt"></i>
                            With Location
                        </button>
                    </div>
                </div>
                
                <!-- Results Section -->
                <div class="results-section">
                    <div class="results-header">
                        <h3>Query Results</h3>
                        <div class="results-info">
                            <span id="resultsCount">No results</span>
                            <span id="queryTime"></span>
                        </div>
                    </div>
                    
                    <div class="loading" id="loading" style="display: none;">
                        <i class="fas fa-spinner fa-spin"></i>
                        <span>Loading data...</span>
                    </div>
                    
                    <div class="error-message" id="errorMessage" style="display: none;">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span id="errorText"></span>
                    </div>
                    
                    <div class="results-container">
                        <div id="resultsTable" class="results-table">
                            <div class="no-results">
                                <i class="fas fa-users"></i>
                                <p>Select a filter above to view user data</p>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Chart.js library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <!-- Common Script -->
    <script src="../assets/js/common.js"></script>
    <!-- Users Script -->
    <script src="../assets/js/users.js"></script>
</body>
</html>
