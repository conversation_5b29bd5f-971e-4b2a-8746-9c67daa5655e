<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StageMinder Admin Console</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.css" rel="stylesheet">
    <link rel="icon" href="data:,">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar Navigation -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <img src="../assets/images/logo.webp" alt="StageMinder Logo" class="logo-img" />
                    <span class="logo-text">StageMinder</span>
                </div>
                <button class="sidebar-toggle" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            
            <nav class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">Main</div>
                    <ul class="nav-list">
                        <li class="nav-item" id="nav-dashboard">
                            <a href="dashboard.html" class="nav-link">
                                <i class="fas fa-tachometer-alt"></i>
                                <span>Dashboard</span>
                            </a>
                        </li>
                        <li class="nav-item" id="nav-users">
                            <a href="users.html" class="nav-link">
                                <i class="fas fa-users"></i>
                                <span>User Management</span>
                            </a>
                        </li>
                        <li class="nav-item" id="nav-acts-venues">
                            <a href="acts-venues.html" class="nav-link">
                                <i class="fas fa-music"></i>
                                <span>Acts & Venues</span>
                            </a>
                        </li>
                        <li class="nav-item" id="nav-analytics">
                            <a href="analytics.html" class="nav-link">
                                <i class="fas fa-chart-line"></i>
                                <span>Analytics</span>
                            </a>
                        </li>
                        <li class="nav-item" id="nav-reports">
                            <a href="reports.html" class="nav-link">
                                <i class="fas fa-file-alt"></i>
                                <span>Reports</span>
                            </a>
                        </li>
                    </ul>
                </div>
                
                <div class="nav-section">
                    <div class="nav-section-title">System</div>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="#settings" class="nav-link" onclick="showSection('settings')">
                                <i class="fas fa-cog"></i>
                                <span>Settings</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#logs" class="nav-link" onclick="showSection('logs')">
                                <i class="fas fa-clipboard-list"></i>
                                <span>System Logs</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
            
            <div class="sidebar-footer">
                <div class="connection-status">
                    <span id="connectionStatus" class="status disconnected">
                        <i class="fas fa-circle"></i>
                        <span>Disconnected</span>
                    </span>
                </div>
            </div>
        </aside>

        <!-- Main Content Area -->
        <div class="main-wrapper">
            <!-- Top Navigation -->
            <header class="top-nav">
                <div class="breadcrumb">
                    <span class="breadcrumb-item">
                        <i class="fas fa-home"></i>
                        Admin Console
                    </span>
                    <span class="breadcrumb-separator">/</span>
                    <span class="breadcrumb-item" id="currentSection">Dashboard</span>
                </div>
                
                <div class="top-nav-actions">
                    <button class="action-btn" onclick="refreshData()" title="Refresh Data">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                    <button class="action-btn" onclick="showNotifications()" title="Notifications">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge">3</span>
                    </button>
                    <button id="mockToggleBtn" class="action-btn" onclick="toggleMockMode()" title="Toggle Mock Data Mode">
                        <i class="fas fa-flask"></i>
                    </button>
                    <div class="user-menu">
                        <button class="user-avatar" onclick="toggleUserMenu()">
                            <i class="fas fa-user-circle"></i>
                            <span>Admin User</span>
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="user-dropdown" id="userDropdown">
                            <a href="#profile" class="dropdown-item">
                                <i class="fas fa-user"></i>
                                Profile
                            </a>
                            <a href="#logout" class="dropdown-item">
                                <i class="fas fa-sign-out-alt"></i>
                                Logout
                            </a>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Main Content -->
            <main class="main-content">
                <!-- Page content will be inserted here -->
                <div id="page-content">
                    <!-- Content placeholder -->
                </div>
            </main>
        </div>
    </div>

    <!-- Chart.js library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <!-- Common Script -->
    <script src="../assets/js/common.js"></script>
    <!-- Page-specific script will be loaded dynamically -->
</body>
</html>
