<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StageMinder Admin Console - Page Tests</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-link {
            display: inline-block;
            padding: 10px 20px;
            background: #3b82f6;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 5px;
            transition: background 0.3s;
        }
        .test-link:hover {
            background: #2563eb;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
        }
        .success { background: #d1fae5; color: #065f46; }
        .error { background: #fee2e2; color: #991b1b; }
        .info { background: #dbeafe; color: #1e40af; }
    </style>
</head>
<body>
    <h1>StageMinder Admin Console - Page Tests</h1>
    
    <div class="test-container">
        <h2>📊 Page Navigation Tests</h2>
        <p>Click each link to test the individual pages:</p>
        
        <a href="pages/dashboard.html" class="test-link" target="_blank">🏠 Dashboard</a>
        <a href="pages/users.html" class="test-link" target="_blank">👥 User Management</a>
        <a href="pages/acts-venues.html" class="test-link" target="_blank">🎵 Acts & Venues</a>
        <a href="pages/analytics.html" class="test-link" target="_blank">📈 Analytics</a>
        <a href="pages/reports.html" class="test-link" target="_blank">📄 Reports</a>
        
        <div class="status info">
            <strong>Test Instructions:</strong>
            <ul>
                <li>Each page should load without errors</li>
                <li>Navigation sidebar should be visible and functional</li>
                <li>Page-specific content should display correctly</li>
                <li>Mock data should load when mock mode is enabled</li>
                <li>Charts should render properly (Analytics page)</li>
            </ul>
        </div>
    </div>
    
    <div class="test-container">
        <h2>🔧 File Structure Verification</h2>
        <div class="status success">
            <strong>✅ Created Files:</strong>
            <ul>
                <li>📁 frontend/pages/ - All 5 main pages</li>
                <li>📁 frontend/assets/css/ - Shared styles</li>
                <li>📁 frontend/assets/js/ - Page-specific scripts</li>
                <li>📁 frontend/assets/images/ - Logo and assets</li>
                <li>📁 frontend/components/ - Shared layout components</li>
            </ul>
        </div>
    </div>
    
    <div class="test-container">
        <h2>🎯 Feature Tests</h2>
        <div class="status info">
            <strong>Key Features to Test:</strong>
            <ul>
                <li><strong>Dashboard:</strong> Statistics cards, quick actions, charts</li>
                <li><strong>Users:</strong> User filtering, search, export functionality</li>
                <li><strong>Acts & Venues:</strong> Statistics display, data filtering</li>
                <li><strong>Analytics:</strong> Chart rendering, data visualization</li>
                <li><strong>Reports:</strong> Report generation, download, search</li>
            </ul>
        </div>
    </div>
    
    <div class="test-container">
        <h2>🚀 Quick Start</h2>
        <div class="status success">
            <p><strong>To access the admin console:</strong></p>
            <ol>
                <li>Open <code>index.html</code> (will redirect to Dashboard)</li>
                <li>Or directly access: <a href="pages/dashboard.html" class="test-link">Dashboard</a></li>
                <li>Use the sidebar to navigate between pages</li>
                <li>Toggle mock mode to test with sample data</li>
            </ol>
        </div>
    </div>
    
    <script>
        // Simple connectivity test
        document.addEventListener('DOMContentLoaded', function() {
            console.log('✅ Test page loaded successfully');
            console.log('📁 File structure created');
            console.log('🔗 Navigation links ready');
            
            // Test if we can access the pages
            const links = document.querySelectorAll('.test-link');
            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    console.log(`🔗 Testing: ${this.textContent} -> ${this.href}`);
                });
            });
        });
    </script>
</body>
</html>
