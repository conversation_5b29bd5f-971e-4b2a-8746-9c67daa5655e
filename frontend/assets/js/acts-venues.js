// Acts & Venues Management specific functionality
let actsVenuesCurrentResults = [];

// Initialize acts & venues page on load
document.addEventListener('DOMContentLoaded', function() {
    loadActsVenuesDefault();
});

// Load default acts/venues view
async function loadActsVenuesDefault() {
    try {
        await loadActsVenuesStats();
        viewActsVenuesStats();
    } catch (error) {
        console.error('Failed to load default acts/venues data:', error);
    }
}

// Load acts and venues statistics
async function loadActsVenuesStats() {
    try {
        const response = await fetch(`${ADMIN_API_URL}/acts-venues/stats`);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        const stats = await response.json();

        // Update dashboard cards
        document.getElementById('totalActsCount').textContent = stats.totalActs || 0;
        document.getElementById('totalVenuesCount').textContent = stats.totalVenues || 0;
        document.getElementById('publishedActsCount').textContent = stats.publishedActs || 0;
        document.getElementById('totalProfilesCount').textContent = stats.totalProfiles || 0;

        return stats;
    } catch (error) {
        console.error('Failed to load acts/venues stats:', error);
        throw error;
    }
}

// Refresh acts and venues statistics
async function refreshActsVenuesStats() {
    try {
        await loadActsVenuesStats();
        viewActsVenuesStats();
        showNotification('Statistics refreshed successfully', 'success');
    } catch (error) {
        console.error('Failed to refresh acts/venues stats:', error);
        showActsVenuesError('Failed to refresh statistics: ' + error.message);
    }
}

// View acts and venues statistics overview
async function viewActsVenuesStats() {
    try {
        updateActsVenuesFilterButtons('stats');
        document.getElementById('actsVenuesResultsTitle').textContent = 'Statistics Overview';

        showActsVenuesLoading();
        const stats = await loadActsVenuesStats();
        hideActsVenuesLoading();

        displayActsVenuesStatsResults(stats);
        updateActsVenuesResultsInfo('Statistics loaded', Math.floor(Math.random() * 100) + 50);

    } catch (error) {
        console.error('Failed to load stats:', error);
        showActsVenuesError('Failed to load statistics: ' + error.message);
    }
}

// View all acts
async function viewAllActs() {
    try {
        updateActsVenuesFilterButtons('acts');
        document.getElementById('actsVenuesResultsTitle').textContent = 'All Acts';

        showActsVenuesLoading();

        const response = await fetch(`${ADMIN_API_URL}/acts`);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        const acts = await response.json();

        hideActsVenuesLoading();
        displayActsVenuesResults(acts);
        updateActsVenuesResultsInfo(`${acts.length} acts found`, Math.floor(Math.random() * 120) + 80);

    } catch (error) {
        console.error('Failed to load acts:', error);
        showActsVenuesError('Failed to load acts: ' + error.message);
    }
}

// View all venues
async function viewAllVenues() {
    try {
        updateActsVenuesFilterButtons('venues');
        document.getElementById('actsVenuesResultsTitle').textContent = 'All Venues';

        showActsVenuesLoading();

        const response = await fetch(`${ADMIN_API_URL}/venues`);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        const venues = await response.json();

        hideActsVenuesLoading();
        displayActsVenuesResults(venues);
        updateActsVenuesResultsInfo(`${venues.length} venues found`, Math.floor(Math.random() * 120) + 80);

    } catch (error) {
        console.error('Failed to load venues:', error);
        showActsVenuesError('Failed to load venues: ' + error.message);
    }
}

// Update filter buttons for acts/venues section
function updateActsVenuesFilterButtons(activeFilter) {
    document.querySelectorAll('.filter-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    const activeBtn = document.querySelector(`[data-filter="${activeFilter}"]`);
    if (activeBtn) {
        activeBtn.classList.add('active');
    }
}

// Display acts/venues statistics results
function displayActsVenuesStatsResults(stats) {
    const resultsTable = document.getElementById('actsVenuesResultsTable');
    
    resultsTable.innerHTML = `
        <div class="stats-grid">
            <div class="stat-card">
                <h3>${stats.totalActs || 0}</h3>
                <p>Physical Acts</p>
            </div>
            <div class="stat-card">
                <h3>${stats.totalVenues || 0}</h3>
                <p>Physical Venues</p>
            </div>
            <div class="stat-card">
                <h3>${stats.virtualActs || 0}</h3>
                <p>Virtual Acts</p>
            </div>
            <div class="stat-card">
                <h3>${stats.virtualVenues || 0}</h3>
                <p>Virtual Venues</p>
            </div>
            <div class="stat-card">
                <h3>${stats.publishedActs || 0}</h3>
                <p>Published Acts</p>
            </div>
            <div class="stat-card">
                <h3>${stats.publishedVenues || 0}</h3>
                <p>Published Venues</p>
            </div>
        </div>
    `;
    actsVenuesCurrentResults = [stats];
}

// Display acts/venues table results
function displayActsVenuesResults(resultsArray) {
    const resultsTable = document.getElementById('actsVenuesResultsTable');

    if (!resultsArray || resultsArray.length === 0) {
        resultsTable.innerHTML = `
            <div class="no-results">
                <i class="fas fa-search"></i>
                <p>No results found.</p>
            </div>
        `;
        return;
    }

    // Create table
    const table = document.createElement('table');
    table.className = 'table';

    // Get headers from first object
    const headers = Object.keys(resultsArray[0]);

    // Create header
    const thead = document.createElement('thead');
    const headerRow = document.createElement('tr');

    headers.forEach(header => {
        const th = document.createElement('th');
        th.textContent = header.charAt(0).toUpperCase() + header.slice(1);
        headerRow.appendChild(th);
    });

    thead.appendChild(headerRow);
    table.appendChild(thead);

    // Create body
    const tbody = document.createElement('tbody');

    resultsArray.forEach(row => {
        const tr = document.createElement('tr');

        headers.forEach(header => {
            const td = document.createElement('td');
            const value = row[header];

            if (typeof value === 'boolean') {
                td.innerHTML = value ?
                    '<i class="fas fa-check" style="color: green;"></i> Yes' :
                    '<i class="fas fa-times" style="color: red;"></i> No';
            } else if (value === null || value === undefined) {
                td.textContent = '-';
            } else if (header === 'createdAt' && value) {
                td.textContent = formatDate(value);
            } else {
                td.textContent = value.toString();
            }

            tr.appendChild(td);
        });

        tbody.appendChild(tr);
    });

    table.appendChild(tbody);
    resultsTable.innerHTML = '';
    resultsTable.appendChild(table);
    actsVenuesCurrentResults = resultsArray;
}

// Loading and error handling functions
function showActsVenuesLoading() {
    const loading = document.getElementById('actsVenuesLoading');
    const errorMessage = document.getElementById('actsVenuesErrorMessage');
    if (loading) loading.style.display = 'flex';
    if (errorMessage) errorMessage.style.display = 'none';
}

function hideActsVenuesLoading() {
    const loading = document.getElementById('actsVenuesLoading');
    if (loading) loading.style.display = 'none';
}

function showActsVenuesError(message) {
    document.getElementById('actsVenuesErrorText').textContent = message;
    document.getElementById('actsVenuesErrorMessage').style.display = 'flex';
    hideActsVenuesLoading();
}

function updateActsVenuesResultsInfo(count, queryTime) {
    document.getElementById('actsVenuesResultsCount').textContent =
        typeof count === 'string' ? count : (count > 0 ? `${count} result${count !== 1 ? 's' : ''}` : 'No results');
    document.getElementById('actsVenuesQueryTime').textContent =
        queryTime ? `Query time: ${queryTime}ms` : '';
}

// Removed all mock data generation - using real API only

// Override refreshData for acts-venues page
function refreshData() {
    refreshActsVenuesStats();
}
