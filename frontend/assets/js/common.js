// Admin Service Configuration
const ADMIN_API_URL = (typeof window !== 'undefined' && window.ADMIN_API_URL) || 'http://localhost:8081/api/admin';

// Global state
let sidebarCollapsed = false;
let userMenuOpen = false;

// Initialize common functionality on page load
document.addEventListener('DOMContentLoaded', function() {
    testConnection();
    initializeEventListeners();
    
    // Set active navigation item based on current page
    setActiveNavigation();

    // Close user menu when clicking outside
    document.addEventListener('click', function(event) {
        if (!event.target.closest('.user-menu')) {
            closeUserMenu();
        }
    });
});

// Set active navigation item based on current page
function setActiveNavigation() {
    const currentPage = window.location.pathname.split('/').pop().replace('.html', '');
    const navItems = document.querySelectorAll('.nav-item');
    
    navItems.forEach(item => {
        item.classList.remove('active');
        const link = item.querySelector('a');
        if (link && link.getAttribute('href').includes(currentPage)) {
            item.classList.add('active');
        }
    });
    
    // Update breadcrumb
    const breadcrumbElement = document.getElementById('currentSection');
    if (breadcrumbElement) {
        const pageNames = {
            'dashboard': 'Dashboard',
            'users': 'User Management',
            'acts-venues': 'Acts & Venues',
            'analytics': 'Analytics',
            'reports': 'Reports'
        };
        breadcrumbElement.textContent = pageNames[currentPage] || 'Dashboard';
    }
}

// Initialize event listeners
function initializeEventListeners() {
    // Sidebar toggle
    const sidebarToggle = document.querySelector('.sidebar-toggle');
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', toggleSidebar);
    }
}

// Sidebar functionality
function toggleSidebar() {
    const sidebar = document.querySelector('.sidebar');
    const mainWrapper = document.querySelector('.main-wrapper');
    
    sidebarCollapsed = !sidebarCollapsed;
    
    if (sidebarCollapsed) {
        sidebar.classList.add('collapsed');
        mainWrapper.classList.add('sidebar-collapsed');
    } else {
        sidebar.classList.remove('collapsed');
        mainWrapper.classList.remove('sidebar-collapsed');
    }
    
    localStorage.setItem('sidebarCollapsed', sidebarCollapsed);
}

// User menu functionality
function toggleUserMenu() {
    const dropdown = document.getElementById('userDropdown');
    userMenuOpen = !userMenuOpen;
    
    if (userMenuOpen) {
        dropdown.style.display = 'block';
        setTimeout(() => dropdown.classList.add('show'), 10);
    } else {
        closeUserMenu();
    }
}

function closeUserMenu() {
    const dropdown = document.getElementById('userDropdown');
    if (dropdown) {
        dropdown.classList.remove('show');
        setTimeout(() => dropdown.style.display = 'none', 200);
    }
    userMenuOpen = false;
}

// Removed Mock mode functionality - now using real API only

// Connection testing
async function testConnection() {
    const statusElement = document.getElementById('connectionStatus');
    if (!statusElement) return;
    
    try {
        const response = await fetch(`${ADMIN_API_URL}/health`, {
            method: 'GET',
            headers: { 'Content-Type': 'application/json' }
        });
        
        if (response.ok) {
            statusElement.className = 'status connected';
            statusElement.innerHTML = '<i class="fas fa-circle"></i><span>Connected</span>';
        } else {
            throw new Error('Server responded with error');
        }
    } catch (error) {
        statusElement.className = 'status disconnected';
        statusElement.innerHTML = '<i class="fas fa-circle"></i><span>Disconnected</span>';
    }
}

// Notification system
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <i class="fas fa-${getNotificationIcon(type)}"></i>
        <span>${message}</span>
        <button class="notification-close" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    // Add to page
    let container = document.querySelector('.notifications-container');
    if (!container) {
        container = document.createElement('div');
        container.className = 'notifications-container';
        document.body.appendChild(container);
    }
    
    container.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

function getNotificationIcon(type) {
    const icons = {
        'info': 'info-circle',
        'success': 'check-circle',
        'warning': 'exclamation-triangle',
        'error': 'times-circle'
    };
    return icons[type] || 'info-circle';
}

// Utility functions
function showNotifications() {
    showNotification('You have 3 new notifications', 'info');
}

function refreshData() {
    showNotification('Data refreshed', 'success');
    // Page-specific refresh logic should be implemented in individual pages
}

// Loading state management
function showLoading(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        element.style.display = 'flex';
    }
}

function hideLoading(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        element.style.display = 'none';
    }
}

// Error handling
function showError(elementId, message) {
    const errorElement = document.getElementById(elementId);
    const errorText = document.getElementById(elementId.replace('Message', 'Text'));
    
    if (errorElement && errorText) {
        errorText.textContent = message;
        errorElement.style.display = 'block';
    }
}

function hideError(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        element.style.display = 'none';
    }
}

// API helper functions
async function makeApiCall(endpoint, options = {}) {
    try {
        const response = await fetch(`${ADMIN_API_URL}${endpoint}`, {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return await response.json();
    } catch (error) {
        console.error('API call failed:', error);
        throw error;
    }
}

// Format utilities
function formatNumber(num) {
    return new Intl.NumberFormat().format(num);
}

function formatDate(date) {
    return new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    }).format(new Date(date));
}

function formatPercentage(value, total) {
    if (total === 0) return '0%';
    return Math.round((value / total) * 100) + '%';
}
