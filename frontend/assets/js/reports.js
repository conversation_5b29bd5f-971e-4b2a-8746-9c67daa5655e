// Reports specific functionality
let mockReports = [];

// Initialize reports page on load
document.addEventListener('DOMContentLoaded', function() {
    attachReportSearch();
    renderReportsList();
});

// Reports mock and UI
function ensureMockReportsReady() {
    if (mockReports.length === 0) {
        const types = ['Users Overview', 'Security Summary', 'Engagement', 'Geo Distribution', 'Providers Mix'];
        for (let i = 0; i < 8; i++) {
            const type = types[i % types.length];
            mockReports.push({
                id: `RPT-${1000 + i}`,
                title: `${type} ${new Date(Date.now() - i * 86400000).toISOString().slice(0,10)}`,
                createdAt: new Date(Date.now() - i * 86400000).toISOString(),
                sizeKb: Math.floor(Math.random() * 770) + 80
            });
        }
    }
}

function renderReportsList() {
    ensureMockReportsReady();
    const container = document.getElementById('reportsList');
    if (!container) return;
    
    const q = (document.getElementById('reportSearch')?.value || '').toLowerCase();
    const filtered = mockReports.filter(r => r.title.toLowerCase().includes(q));
    document.getElementById('reportsCount').textContent = `${filtered.length} items`;
    
    container.innerHTML = '';
    
    if (filtered.length === 0) {
        container.innerHTML = `
            <div class="no-results">
                <i class="fas fa-file-alt"></i>
                <p>No reports found matching your search.</p>
            </div>
        `;
        return;
    }
    
    filtered.forEach(r => {
        const div = document.createElement('div');
        div.className = 'report-card';
        div.innerHTML = `
            <div class="report-header">
                <div class="report-icon">
                    <i class="fas fa-file-alt"></i>
                </div>
                <div class="report-info">
                    <div class="report-title">${r.title}</div>
                    <div class="report-meta">
                        <span class="report-id">ID: ${r.id}</span>
                        <span class="report-size">${Math.round(r.sizeKb)} KB</span>
                        <span class="report-date">${formatDate(r.createdAt)}</span>
                    </div>
                </div>
            </div>
            <div class="report-actions">
                <button class="btn secondary" onclick="downloadReport('${r.id}')" title="Download Report">
                    <i class="fas fa-download"></i> Download
                </button>
                <button class="btn secondary" onclick="deleteReport('${r.id}')" title="Delete Report">
                    <i class="fas fa-trash"></i> Delete
                </button>
            </div>
        `;
        container.appendChild(div);
    });
}

function generateReport() {
    ensureMockReportsReady();
    const id = `RPT-${1000 + mockReports.length}`;
    const title = `Custom Report ${new Date().toISOString().slice(0,19).replace('T',' ')}`;
    
    // Show generating notification
    showNotification('Generating report...', 'info');
    
    // Simulate report generation delay
    setTimeout(() => {
        mockReports.unshift({
            id,
            title,
            createdAt: new Date().toISOString(),
            sizeKb: Math.floor(Math.random() * 860) + 120
        });
        renderReportsList();
        showNotification('Report generated successfully', 'success');
    }, 1500);
}

function downloadReport(id) {
    const rpt = mockReports.find(r => r.id === id);
    if (!rpt) {
        showNotification('Report not found', 'error');
        return;
    }
    
    // Create comprehensive report content
    const content = `# StageMinder Admin Report
Report ID: ${rpt.id}
Title: ${rpt.title}
Generated At: ${rpt.createdAt}
Size: ${Math.round(rpt.sizeKb)} KB

## Summary
This is a comprehensive system report generated by the StageMinder Admin Console.

## System Statistics
- Total Users: ${Math.floor(Math.random() * 400) + 100}
- Active Users: ${Math.floor(Math.random() * 320) + 80}
- Total Acts: ${Math.floor(Math.random() * 150) + 50}
- Total Venues: ${Math.floor(Math.random() * 80) + 20}

## Performance Metrics
- Average Response Time: ${Math.floor(Math.random() * 150) + 50}ms
- System Uptime: ${Math.floor(Math.random() * 4) + 95}.${Math.floor(Math.random() * 10)}%
- Database Health: Excellent

## Security Overview
- 2FA Adoption Rate: ${Math.floor(Math.random() * 30) + 60}%
- Failed Login Attempts: ${Math.floor(Math.random() * 11)}
- Security Incidents: 0

## Recommendations
1. Continue monitoring user engagement metrics
2. Regular security audits recommended
3. Consider expanding venue partnerships

---
Generated by StageMinder Admin Console
Report Format: Text Document
Timestamp: ${new Date().toISOString()}
`;

    const blob = new Blob([content], { type: 'text/plain;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${rpt.id}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    showNotification(`Report ${rpt.id} downloaded successfully`, 'success');
}

function deleteReport(id) {
    const rpt = mockReports.find(r => r.id === id);
    if (!rpt) {
        showNotification('Report not found', 'error');
        return;
    }
    
    if (confirm(`Are you sure you want to delete report "${rpt.title}"?`)) {
        mockReports = mockReports.filter(r => r.id !== id);
        renderReportsList();
        showNotification('Report deleted successfully', 'success');
    }
}

function attachReportSearch() {
    const input = document.getElementById('reportSearch');
    if (!input) return;
    
    input.addEventListener('input', renderReportsList);
    
    // Add placeholder functionality
    input.addEventListener('focus', function() {
        this.placeholder = 'Type to search reports...';
    });
    
    input.addEventListener('blur', function() {
        this.placeholder = 'Search reports...';
    });
}

// Report type templates
function generateSpecificReport(type) {
    ensureMockReportsReady();
    const id = `RPT-${1000 + mockReports.length}`;
    const title = `${type} Report ${new Date().toISOString().slice(0,10)}`;
    
    showNotification(`Generating ${type} report...`, 'info');
    
    setTimeout(() => {
        mockReports.unshift({
            id,
            title,
            createdAt: new Date().toISOString(),
            sizeKb: Math.floor(Math.random() * 1050) + 150
        });
        renderReportsList();
        showNotification(`${type} report generated successfully`, 'success');
    }, Math.floor(Math.random() * 1500) + 1000);
}

// Quick report generation functions
function generateUsersReport() {
    generateSpecificReport('Users Overview');
}

function generateSecurityReport() {
    generateSpecificReport('Security Summary');
}

function generateEngagementReport() {
    generateSpecificReport('Engagement Analysis');
}

function generateGeoReport() {
    generateSpecificReport('Geographic Distribution');
}

function generateProvidersReport() {
    generateSpecificReport('Authentication Providers');
}

// Removed randomInt utility function - using Math.random() directly

// Override refreshData for reports page
function refreshData() {
    renderReportsList();
    showNotification('Reports list refreshed', 'success');
}

// Export all reports functionality
function exportAllReports() {
    if (mockReports.length === 0) {
        showNotification('No reports to export', 'warning');
        return;
    }
    
    const csvContent = [
        'ID,Title,Created At,Size (KB)',
        ...mockReports.map(r => `${r.id},"${r.title}",${r.createdAt},${Math.round(r.sizeKb)}`)
    ].join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `reports_export_${new Date().toISOString().slice(0, 10)}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
    
    showNotification('Reports list exported successfully', 'success');
}
