// Users Management specific functionality
let currentResults = [];
let usersInitialLoaded = false;

// Initialize users page on load
document.addEventListener('DOMContentLoaded', function() {
    attachSearchFilter();
    loadUsersDefault();
});

// Enhanced Query Functions with UI Updates
function updateFilterButtons(activeFilter) {
    document.querySelectorAll('.filter-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    const activeBtn = document.querySelector(`[data-filter="${activeFilter}"]`);
    if (activeBtn) {
        activeBtn.classList.add('active');
    }
}

// Show/hide loading state
function showLoading() {
    const loading = document.getElementById('loading');
    const errorMessage = document.getElementById('errorMessage');
    if (loading) loading.style.display = 'flex';
    if (errorMessage) errorMessage.style.display = 'none';
}

function hideLoading() {
    const loading = document.getElementById('loading');
    if (loading) loading.style.display = 'none';
}

// Show error message
function showError(message) {
    document.getElementById('errorText').textContent = message;
    document.getElementById('errorMessage').style.display = 'flex';
    hideLoading();
}

// Update results info
function updateResultsInfo(count, queryTime) {
    document.getElementById('resultsCount').textContent = 
        count > 0 ? `${count} result${count !== 1 ? 's' : ''}` : 'No results';
    document.getElementById('queryTime').textContent = 
        queryTime ? `Query time: ${queryTime}ms` : '';
}

// Execute API call to Admin Service
async function executeAPICall(endpoint) {
    showLoading();

    try {
        const startTime = Date.now();
        const response = await fetch(`${ADMIN_API_URL}${endpoint}`);
        const queryTime = Date.now() - startTime;

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const results = await response.json();
        hideLoading();
        displayResults(results);
        updateResultsInfo(Array.isArray(results) ? results.length : 1, queryTime);
        return results;
    } catch (error) {
        console.error('API call failed:', error);
        showError('Failed to fetch data: ' + error.message);
    }
}

// Display query results
function displayResults(results) {
    const resultsTable = document.getElementById('resultsTable');
    const resultsArray = Array.isArray(results) ? results : (results ? [results] : []);

    if (resultsArray.length === 0) {
        resultsTable.innerHTML = `
            <div class="no-results">
                <i class="fas fa-search"></i>
                <p>No results found for this query.</p>
            </div>
        `;
        return;
    }
    
    // Check if this is a statistics query
    const firstResult = resultsArray[0];
    if (firstResult.hasOwnProperty('totalUsers') || 
        firstResult.hasOwnProperty('enabledUsers') || 
        firstResult.hasOwnProperty('twoFaUsers')) {
        displayStatsResults(firstResult);
        currentResults = [firstResult];
        return;
    }

    // Create table for user data
    const table = document.createElement('table');
    table.className = 'table';
    
    // Create header
    const thead = document.createElement('thead');
    const headerRow = document.createElement('tr');
    
    const headers = ['ID', 'Username', 'Email', 'Role', 'Provider', 'Enabled', '2FA', 'Location', 'Last Login'];
    headers.forEach(header => {
        const th = document.createElement('th');
        th.textContent = header;
        headerRow.appendChild(th);
    });
    
    thead.appendChild(headerRow);
    table.appendChild(thead);
    
    // Create body
    const tbody = document.createElement('tbody');
    
    resultsArray.forEach(user => {
        const row = document.createElement('tr');
        
        // ID
        const idCell = document.createElement('td');
        idCell.textContent = user.id || '-';
        row.appendChild(idCell);
        
        // Username
        const usernameCell = document.createElement('td');
        usernameCell.innerHTML = `
            <div class="user-info">
                <div class="user-avatar-small">
                    <i class="fas fa-user"></i>
                </div>
                <div>
                    <div class="user-name">${user.username || '-'}</div>
                    ${user.isArtist ? '<span class="user-badge artist">Artist</span>' : ''}
                </div>
            </div>
        `;
        row.appendChild(usernameCell);
        
        // Email
        const emailCell = document.createElement('td');
        emailCell.textContent = user.email || '-';
        row.appendChild(emailCell);
        
        // Role
        const roleCell = document.createElement('td');
        roleCell.innerHTML = `<span class="role-badge ${(user.role || '').toLowerCase()}">${user.role || '-'}</span>`;
        row.appendChild(roleCell);
        
        // Provider
        const providerCell = document.createElement('td');
        const providerIcon = getProviderIcon(user.provider);
        providerCell.innerHTML = `<i class="${providerIcon}"></i> ${user.provider || '-'}`;
        row.appendChild(providerCell);
        
        // Enabled
        const enabledCell = document.createElement('td');
        enabledCell.innerHTML = user.enabled ? 
            '<i class="fas fa-check" style="color: green;"></i> Yes' : 
            '<i class="fas fa-times" style="color: red;"></i> No';
        row.appendChild(enabledCell);
        
        // 2FA
        const twoFaCell = document.createElement('td');
        twoFaCell.innerHTML = user.has2FA ? 
            '<i class="fas fa-shield-alt" style="color: green;"></i> Yes' : 
            '<i class="fas fa-shield-alt" style="color: gray;"></i> No';
        row.appendChild(twoFaCell);
        
        // Location
        const locationCell = document.createElement('td');
        locationCell.textContent = user.city && user.country ? `${user.city}, ${user.country}` : '-';
        row.appendChild(locationCell);
        
        // Last Login
        const lastLoginCell = document.createElement('td');
        lastLoginCell.textContent = user.lastLogin ? formatDate(user.lastLogin) : '-';
        row.appendChild(lastLoginCell);
        
        tbody.appendChild(row);
    });
    
    table.appendChild(tbody);
    resultsTable.innerHTML = '';
    resultsTable.appendChild(table);
    currentResults = resultsArray;
    // Apply current search filter to newly rendered table
    applySearchFilter();
}

// Display statistics results
function displayStatsResults(stats) {
    const resultsTable = document.getElementById('resultsTable');

    resultsTable.innerHTML = `
        <div class="stats-grid">
            <div class="stat-card">
                <h3>${stats.totalUsers || 0}</h3>
                <p>Total Users</p>
            </div>
            <div class="stat-card">
                <h3>${stats.enabledUsers || 0}</h3>
                <p>Enabled Users</p>
            </div>
            <div class="stat-card">
                <h3>${stats.twoFaUsers || 0}</h3>
                <p>Two-Factor Auth Users</p>
            </div>
            <div class="stat-card">
                <h3>${stats.socialLoginUsers || 0}</h3>
                <p>Social Login Users</p>
            </div>
        </div>
    `;
}

// Query Functions
async function queryAllUsers() {
    updateFilterButtons('all');
    await executeAPICall('/users');
}

function loadUsersDefault() {
    if (usersInitialLoaded) return;
    // ensure filter button visual state is correct
    updateFilterButtons('all');
    // load real data from API
    queryAllUsers();
    usersInitialLoaded = true;
}

async function queryUsersWithLocation() {
    updateFilterButtons('location');
    await executeAPICall('/users/with-location');
}

async function queryArtistUsers() {
    updateFilterButtons('artists');
    await executeAPICall('/users/artists');
}

async function queryEnabledUsers() {
    updateFilterButtons('enabled');
    await executeAPICall('/users/enabled');
}

// Removed all mock API simulation - using real API only

// Utility functions - removed mock-related utilities

// Search filter
function attachSearchFilter() {
    const input = document.getElementById('userSearch');
    if (!input) return;
    input.addEventListener('input', applySearchFilter);
}

function applySearchFilter() {
    const input = document.getElementById('userSearch');
    const q = (input?.value || '').trim().toLowerCase();
    const table = document.querySelector('#resultsTable table');
    if (!table) return;
    const rows = table.querySelectorAll('tbody tr');
    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        row.style.display = text.includes(q) ? '' : 'none';
    });
}

// Provider icon helper
function getProviderIcon(provider) {
    const icons = {
        'local': 'fas fa-user',
        'google': 'fab fa-google',
        'facebook': 'fab fa-facebook',
        'github': 'fab fa-github',
        'apple': 'fab fa-apple'
    };
    return icons[provider] || 'fas fa-user';
}

// User management actions
function addUser() {
    // This would typically open a modal or form for adding a new user
    // For now, just show a notification that this feature would be implemented
    showNotification('Add user functionality would be implemented here', 'info');

    // In a real implementation, this would:
    // 1. Open a form modal
    // 2. Collect user data
    // 3. POST to /users endpoint
    // 4. Refresh the current view
}

function exportUsers() {
    if (currentResults.length === 0) {
        showNotification('No data to export', 'warning');
        return;
    }

    // Create CSV content
    const headers = ['ID', 'Username', 'Email', 'Role', 'Provider', 'Enabled', '2FA', 'City', 'Country', 'Last Login'];
    const csvContent = [
        headers.join(','),
        ...currentResults.map(user => [
            user.id || '',
            user.username || '',
            user.email || '',
            user.role || '',
            user.provider || '',
            user.enabled ? 'Yes' : 'No',
            user.has2FA ? 'Yes' : 'No',
            user.city || '',
            user.country || '',
            user.lastLogin || ''
        ].join(','))
    ].join('\n');

    // Download CSV
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `users_export_${new Date().toISOString().slice(0, 10)}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);

    showNotification('Users data exported successfully', 'success');
}

// Override refreshData for users page
function refreshData() {
    // Refresh current user query if any
    const activeFilter = document.querySelector('.filter-btn.active');
    if (activeFilter) {
        const filterType = activeFilter.getAttribute('data-filter');
        switch (filterType) {
            case 'all':
                queryAllUsers();
                break;
            case 'enabled':
                queryEnabledUsers();
                break;
            case 'artists':
                queryArtistUsers();
                break;
            case 'location':
                queryUsersWithLocation();
                break;
        }
    }
    showNotification('User data refreshed', 'success');
}
