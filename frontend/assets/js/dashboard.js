// Dashboard-specific functionality
let currentResults = [];
let dauChartInstance = null;
let providersChartDashboardInstance = null;

// Initialize dashboard on page load
document.addEventListener('DOMContentLoaded', function() {
    loadDashboardStats();
    loadTopArtists();
});

// Dashboard Functions
async function loadDashboardStats() {
    try {
        const stats = await fetchStatsForDashboard();
        updateDashboardCards(stats);
        updateDashboardWidgets(stats);
    } catch (error) {
        console.log('Dashboard stats not available yet');
        updateDashboardCards({
            totalUsers: 0,
            enabledUsers: 0,
            twoFaUsers: 0,
            socialLoginUsers: 0
        });
        updateDashboardWidgets({ totalUsers: 0, enabledUsers: 0, twoFaUsers: 0, socialLoginUsers: 0 });
    }
}

function updateDashboardCards(stats) {
    document.getElementById('totalUsersCount').textContent = stats.totalUsers || 0;
    document.getElementById('activeUsersCount').textContent = stats.enabledUsers || 0;
    document.getElementById('secureUsersCount').textContent = stats.twoFaUsers || 0;
}

// Dashboard widgets rendering
async function updateDashboardWidgets(stats) {
    // progress bars
    const total = Math.max(1, stats.totalUsers || 0);
    const pct2fa = Math.round(((stats.twoFaUsers || 0) / total) * 100);
    const pctActive = Math.round(((stats.enabledUsers || 0) / total) * 100);
    const pctSocial = Math.round(((stats.socialLoginUsers || 0) / total) * 100);
    const setBar = (idBar, idPct, pct) => {
        const bar = document.getElementById(idBar);
        const pctEl = document.getElementById(idPct);
        if (bar && pctEl) { bar.style.width = pct + '%'; pctEl.textContent = pct + '%'; }
    };
    setBar('prog2fa', 'prog2faPct', pct2fa);
    setBar('progActive', 'progActivePct', pctActive);
    setBar('progSocial', 'progSocialPct', pctSocial);

    // charts: DAU and providers - get real data from API
    try {
        const [dauData, providerData] = await Promise.all([
            fetch(`${ADMIN_API_URL}/metrics/dau/30d`).then(r => r.json()),
            fetch(`${ADMIN_API_URL}/metrics/providers`).then(r => r.json())
        ]);

        const days = dauData.map(d => d.date);
        const dau = dauData.map(d => d.count);
        const provCounts = providerData;

        const dauCtx = document.getElementById('dauChart');
        if (dauCtx) {
            if (dauChartInstance) dauChartInstance.destroy();
            dauChartInstance = new Chart(dauCtx, {
                type: 'line',
                data: { labels: days, datasets: [{ label: 'DAU', data: dau, borderColor: '#2563eb', backgroundColor: 'rgba(37,99,235,.15)', tension: .35, fill: true }] },
                options: { plugins: { legend: { display: false } }, scales: { y: { beginAtZero: true } } }
            });
        }

        const provCtx = document.getElementById('providersChartDashboard');
        if (provCtx) {
            if (providersChartDashboardInstance) providersChartDashboardInstance.destroy();
            providersChartDashboardInstance = new Chart(provCtx, {
                type: 'doughnut',
                data: { labels: Object.keys(provCounts).map(p => p[0].toUpperCase() + p.slice(1)), datasets: [{ data: Object.values(provCounts), backgroundColor: ['#64748b','#ef4444','#10b981','#f59e0b','#3b82f6'] }] },
                options: { plugins: { legend: { position: 'bottom' } } }
            });
        }
    } catch (error) {
        console.error('Failed to load chart data:', error);
        showNotification('Failed to load chart data', 'error');
    }

    // Top artists table
    loadTopArtists();
}

// Load top artists for dashboard widget
async function loadTopArtists() {
    try {
        const response = await fetch(`${ADMIN_API_URL}/users/artists/top?limit=5`);
        if (!response.ok) {
            throw new Error('Failed to fetch top artists');
        }
        const artists = await response.json();

        const tbody = document.getElementById('topArtistsBody');
        if (tbody) {
            tbody.innerHTML = artists.map(artist => `
                <tr>
                    <td>
                        <div class="user-info">
                            <div class="user-avatar-small">
                                <i class="fas fa-user"></i>
                            </div>
                            <div>
                                <div class="user-name">${artist.username || 'N/A'}</div>
                                <div class="user-email">${artist.email || 'N/A'}</div>
                            </div>
                        </div>
                    </td>
                    <td>${formatNumber(artist.followers || 0)}</td>
                    <td>${formatNumber(artist.posts || 0)}</td>
                    <td>${artist.lastLogin ? formatDate(artist.lastLogin) : 'Never'}</td>
                </tr>
            `).join('');
        }
    } catch (error) {
        console.error('Failed to load top artists:', error);
        const tbody = document.getElementById('topArtistsBody');
        if (tbody) {
            tbody.innerHTML = '<tr><td colspan="4">Failed to load top artists</td></tr>';
        }
    }
}

// Quick action functions
async function queryAllUsers() {
    window.location.href = 'users.html';
}

async function queryUserStats() {
    try {
        const data = await fetchStatsForDashboard();
        updateDashboardCards(data);
        showNotification('User statistics refreshed', 'success');
    } catch (error) {
        console.error('Failed to load user stats:', error);
        showNotification('Failed to refresh statistics', 'error');
    }
}

function checkSystemHealth() {
    // Simulate system health check
    showNotification('System health check initiated...', 'info');
    
    setTimeout(() => {
        const healthScore = randomInt(85, 99);
        document.getElementById('systemHealthScore').textContent = healthScore + '%';
        showNotification(`System health: ${healthScore}% - All systems operational`, 'success');
    }, 2000);
}

// Data fetching functions
async function fetchStatsForDashboard() {
    const response = await fetch(`${ADMIN_API_URL}/users/stats`);
    if (!response.ok) throw new Error('Failed to fetch stats');
    return response.json();
}

// Removed all mock data generation functions - using real API data only

// Override refreshData for dashboard
function refreshData() {
    loadDashboardStats();
    showNotification('Dashboard data refreshed', 'success');
}
