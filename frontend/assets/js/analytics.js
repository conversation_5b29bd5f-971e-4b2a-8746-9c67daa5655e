// Analytics specific functionality
let chartsInitialized = false;

// Initialize analytics page on load
document.addEventListener('DOMContentLoaded', function() {
    // Initialize charts after a short delay to ensure DOM is ready
    setTimeout(initChartsIfNeeded, 100);
});

// Analytics charts (Chart.js)
async function initChartsIfNeeded() {
    if (chartsInitialized) return;
    const ctx1 = document.getElementById('usersGrowthChart');
    const ctx2 = document.getElementById('providerBreakdownChart');
    const ctx3 = document.getElementById('topCountriesChart');
    if (!ctx1 || !ctx2 || !ctx3) return;
    chartsInitialized = true;

    try {
        const [contractsResp, eventsResp, byCountryResp, eventsStatsResp, profilesResp] = await Promise.all([
            fetch(`${ADMIN_API_URL}/metrics/contracts/series30d`),
            fetch(`${ADMIN_API_URL}/metrics/events/series30d`),
            fetch(`${ADMIN_API_URL}/metrics/users/by-country`),
            fetch(`${ADMIN_API_URL}/metrics/events`),
            fetch(`${ADMIN_API_URL}/metrics/profiles`)
        ]);

        const seriesContracts = await contractsResp.json();
        const seriesEvents = await eventsResp.json();
        const usersByCountry = await byCountryResp.json();
        const eventsStats = await eventsStatsResp.json();
        const eventByStatus = eventsStats.byStatus || {};
        const profileStats = await profilesResp.json();

        // Contracts Created Chart
        const labels30d = seriesContracts.map(p => p.date);
        const contractCounts = seriesContracts.map(p => p.count);
        new Chart(ctx1, { 
            type: 'line', 
            data: { 
                labels: labels30d, 
                datasets: [{ 
                    label: 'Contracts', 
                    data: contractCounts, 
                    borderColor: '#3b82f6', 
                    backgroundColor: 'rgba(59,130,246,.2)', 
                    tension: .35, 
                    fill: true 
                }] 
            }, 
            options: { 
                plugins: { legend: { display: false } }, 
                scales: { y: { beginAtZero: true } } 
            } 
        });

        // Events Scheduled Chart
        const labels30dEv = seriesEvents.map(p => p.date);
        const eventCounts = seriesEvents.map(p => p.count);
        new Chart(ctx2, { 
            type: 'bar', 
            data: { 
                labels: labels30dEv, 
                datasets: [{ 
                    label: 'Events', 
                    data: eventCounts, 
                    backgroundColor: '#10b981' 
                }] 
            }, 
            options: { 
                plugins: { legend: { display: false } }, 
                scales: { y: { beginAtZero: true } } 
            } 
        });

        // Users by Country Chart
        const countryLabels = usersByCountry.map(x => x.country);
        const countryVals = usersByCountry.map(x => x.users);
        new Chart(ctx3, { 
            type: 'bar', 
            data: { 
                labels: countryLabels, 
                datasets: [{ 
                    label: 'Users', 
                    data: countryVals, 
                    backgroundColor: '#06b6d4' 
                }] 
            }, 
            options: { 
                plugins: { legend: { display: false } }, 
                scales: { y: { beginAtZero: true } } 
            } 
        });

        // Event Status Distribution Chart
        const statusCtx = document.getElementById('eventStatusChart');
        if (statusCtx) {
            const sLabels = Object.keys(eventByStatus);
            const sVals = sLabels.map(k => eventByStatus[k]);
            new Chart(statusCtx, { 
                type: 'doughnut', 
                data: { 
                    labels: sLabels, 
                    datasets: [{ 
                        data: sVals, 
                        backgroundColor: ['#22c55e','#f59e0b','#ef4444','#3b82f6','#a855f7','#06b6d4'] 
                    }] 
                }, 
                options: { 
                    plugins: { legend: { position: 'bottom' } } 
                } 
            });
        }

        // Act vs Venue Chart
        const avCtx = document.getElementById('actVenueChart');
        if (avCtx) {
            const labels = ['ACT', 'VENUE', 'VIRTUAL_ACT', 'VIRTUAL_VENUE'];
            const vals = [
                profileStats.actProfiles||0, 
                profileStats.venueProfiles||0, 
                profileStats.virtualActProfiles||0, 
                profileStats.virtualVenueProfiles||0
            ];
            new Chart(avCtx, { 
                type: 'bar', 
                data: { 
                    labels, 
                    datasets: [{ 
                        label: 'Profiles', 
                        data: vals, 
                        backgroundColor: ['#3b82f6','#10b981','#a855f7','#f97316'] 
                    }] 
                }, 
                options: { 
                    plugins: { legend: { display: false } }, 
                    scales: { y: { beginAtZero: true } } 
                } 
            });
        }

        // Profile Status Chart
        const pstatCtx = document.getElementById('profileStatusChart');
        if (pstatCtx) {
            const labels = ['Published', 'Created', 'Deleted'];
            const vals = [
                profileStats.publishedProfiles||0, 
                profileStats.createdProfiles||0, 
                profileStats.deletedProfiles||0
            ];
            new Chart(pstatCtx, { 
                type: 'pie', 
                data: { 
                    labels, 
                    datasets: [{ 
                        data: vals, 
                        backgroundColor: ['#22c55e','#eab308','#ef4444'] 
                    }] 
                }, 
                options: { 
                    plugins: { legend: { position: 'bottom' } } 
                } 
            });
        }
    } catch (e) {
        console.error('Failed to init charts', e);
        showNotification('Failed to load analytics data', 'error');
    }
}

// Removed all mock data functions - using real API only

// Override refreshData for analytics page
function refreshData() {
    chartsInitialized = false;
    initChartsIfNeeded();
    showNotification('Analytics data refreshed', 'success');
}
