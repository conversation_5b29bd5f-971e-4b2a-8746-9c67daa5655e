/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, 'Roboto', sans-serif;
    background: #f8fafc;
    min-height: 100vh;
    color: #1e293b;
    line-height: 1.6;
}

:root {
    --primary-color: #3b82f6;
    --primary-dark: #2563eb;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;
    
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 80px;
    --header-height: 70px;
    
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --border-radius-xl: 16px;
}

.admin-layout {
    display: flex;
    min-height: 100vh;
    background: var(--gray-50);
}

/* Sidebar Styles */
.sidebar {
    width: var(--sidebar-width);
    background: white;
    border-right: 1px solid var(--gray-200);
    display: flex;
    flex-direction: column;
    position: fixed;
    height: 100vh;
    z-index: 1000;
    box-shadow: var(--shadow-lg);
    transition: width 0.3s ease;
}

.sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.logo-img { height: 20px; width: auto; display: block; object-fit: contain; }

.logo-text {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--gray-800);
    white-space: nowrap;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--gray-500);
    font-size: 1.25rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    transition: all 0.2s ease;
}

.sidebar-toggle:hover {
    background: var(--gray-100);
    color: var(--gray-700);
}

.sidebar-nav {
    flex: 1;
    padding: 1rem 0;
    overflow-y: auto;
}

.nav-section {
    margin-bottom: 2rem;
}

.nav-section-title {
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    color: var(--gray-500);
    margin-bottom: 0.75rem;
    padding: 0 1.5rem;
    letter-spacing: 0.05em;
}

.nav-list {
    list-style: none;
}

.nav-item {
    margin-bottom: 0.25rem;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.5rem;
    color: var(--gray-600);
    text-decoration: none;
    transition: all 0.2s ease;
    border-right: 3px solid transparent;
}

.nav-link:hover {
    background: var(--gray-50);
    color: var(--gray-800);
}

.nav-item.active .nav-link {
    background: var(--primary-color);
    color: white;
    border-right-color: var(--primary-dark);
}

.nav-link i {
    font-size: 1.125rem;
    width: 1.5rem;
    text-align: center;
}

.sidebar-footer {
    padding: 1.5rem;
    border-top: 1px solid var(--gray-200);
}

.connection-status .status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    border-radius: var(--border-radius);
    font-weight: 500;
    font-size: 0.875rem;
    border: 1px solid;
}

.status.connected {
    background: #dcfce7;
    color: #166534;
    border-color: #bbf7d0;
}

.status.disconnected {
    background: #fef2f2;
    color: #dc2626;
    border-color: #fecaca;
}

.status.connecting {
    background: #fefce8;
    color: #ca8a04;
    border-color: #fef08a;
}

/* Main Wrapper */
.main-wrapper {
    flex: 1;
    margin-left: var(--sidebar-width);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    transition: margin-left 0.3s ease;
}

.sidebar.collapsed + .main-wrapper {
    margin-left: var(--sidebar-collapsed-width);
}

/* Top Navigation */
.top-nav {
    height: var(--header-height);
    background: white;
    border-bottom: 1px solid var(--gray-200);
    display: grid;
    grid-template-columns: 1fr auto;
    align-items: center;
    gap: 1rem;
    padding: 0 2rem;
    box-shadow: var(--shadow-sm);
    z-index: 900;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: var(--gray-600);
}

.breadcrumb-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.breadcrumb-separator {
    color: var(--gray-400);
}

.top-nav-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
    justify-content: flex-end;
}

.action-btn {
    position: relative;
    background: none;
    border: none;
    color: var(--gray-500);
    font-size: 1.125rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    transition: all 0.2s ease;
}

.action-btn:hover {
    background: var(--gray-100);
    color: var(--gray-700);
}

.notification-badge {
    position: absolute;
    top: 0.25rem;
    right: 0.25rem;
    background: var(--danger-color);
    color: white;
    font-size: 0.75rem;
    padding: 0.125rem 0.375rem;
    border-radius: 10px;
    min-width: 1.25rem;
    text-align: center;
}

.user-menu {
    position: relative;
}

.user-avatar {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: none;
    border: none;
    color: var(--gray-700);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    transition: all 0.2s ease;
}

.user-avatar:hover {
    background: var(--gray-100);
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    min-width: 180px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease;
}

.user-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    color: var(--gray-700);
    text-decoration: none;
    transition: background 0.2s ease;
}

.dropdown-item:hover {
    background: var(--gray-50);
}

/* Main Content */
.main-content {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
}

/* Content Sections */
.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

.page-header {
    margin-bottom: 2rem;
}

.page-title {
    font-size: 1.875rem;
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: 0.5rem;
}

.page-description {
    color: var(--gray-600);
    font-size: 1rem;
}

/* Dashboard Styles */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.2s ease;
}

.stat-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.stat-icon {
    width: 3rem;
    height: 3rem;
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-card.primary .stat-icon {
    background: var(--primary-color);
}

.stat-card.success .stat-icon {
    background: var(--success-color);
}

.stat-card.warning .stat-icon {
    background: var(--warning-color);
}

.stat-card.info .stat-icon {
    background: var(--info-color);
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gray-800);
    line-height: 1;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--gray-600);
    margin: 0.5rem 0;
}

.stat-change {
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.stat-change.positive {
    color: var(--success-color);
}

.stat-change.negative {
    color: var(--danger-color);
}

/* Dashboard Section */
.dashboard-section {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    margin-bottom: 2rem;
}

.section-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 1rem;
}

.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.action-card {
    background: var(--gray-50);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
}

.action-card:hover {
    background: white;
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.action-card i {
    font-size: 2rem;
    color: var(--primary-color);
}

.action-card span {
    font-weight: 600;
    color: var(--gray-800);
}

.action-description {
    font-size: 0.875rem;
    color: var(--gray-600);
}

/* Dashboard widgets */
.dashboard-widgets-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 1.5rem;
}

.widget-card {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    padding: 1rem 1rem 1.25rem;
}

.widget-title {
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.progress-group { display: flex; flex-direction: column; gap: .75rem; }
.progress-row { display: grid; grid-template-columns: 160px 1fr 60px; align-items: center; gap: .75rem; }
.progress-label { color: var(--gray-600); font-size: .875rem; }
.progress { height: 10px; background: var(--gray-200); border-radius: 999px; overflow: hidden; }
.progress-bar { height: 100%; background: var(--warning-color); }
.progress-bar.success { background: var(--success-color); }
.progress-bar.info { background: var(--info-color); }
.progress-pct { text-align: right; color: var(--gray-700); font-weight: 600; font-size: .875rem; }

/* Toolbar */
.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    gap: 1rem;
}

.search-box {
    position: relative;
    flex: 1;
    max-width: 400px;
}

.search-box i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-400);
}

.search-box input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    transition: border-color 0.2s ease;
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}

.toolbar-actions {
    display: flex;
    gap: 0.75rem;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    border: none;
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
}

.btn.primary {
    background: var(--primary-color);
    color: white;
}

.btn.primary:hover {
    background: var(--primary-dark);
}

.btn.secondary {
    background: white;
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
}

.btn.secondary:hover {
    background: var(--gray-50);
}

/* Filter Panel */
.filter-panel {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    margin-bottom: 1.5rem;
}

.filter-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 1rem;
}

.filter-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
}

.filter-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    background: white;
    color: var(--gray-700);
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.filter-btn:hover {
    background: var(--gray-50);
}

.filter-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Results Section */
.results-section {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--gray-200);
}

.results-header h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-800);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.results-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.25rem;
    font-size: 0.875rem;
    color: var(--gray-600);
}

.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    padding: 3rem;
    color: var(--primary-color);
    font-size: 1rem;
}

.loading i {
    font-size: 1.5rem;
}

.error-message {
    background: #fef2f2;
    color: #dc2626;
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    border: 1px solid #fecaca;
}

.results-container {
    max-height: 600px;
    overflow: auto;
}

.results-table {
    width: 100%;
}

.table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
}

.table th,
.table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--gray-200);
}

.table th {
    background: var(--gray-50);
    font-weight: 600;
    color: var(--gray-700);
    position: sticky;
    top: 0;
    font-size: 0.875rem;
}

.table tbody tr {
    transition: background 0.2s ease;
}

.table tbody tr:hover {
    background: var(--gray-50);
}

.no-results {
    text-align: center;
    padding: 3rem;
    color: var(--gray-500);
}

.no-results i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--gray-300);
}

/* Placeholder Content */
.placeholder-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    text-align: center;
    color: var(--gray-500);
    background: white;
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--gray-200);
}

.placeholder-content i {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: var(--gray-300);
}

.placeholder-content p {
    font-size: 1.125rem;
}

/* Analytics */
.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.chart-card {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    padding: 1rem 1rem 1.25rem;
    box-shadow: var(--shadow-md);
}

.chart-title {
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Reports */
.reports-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
    gap: 1rem;
}

.report-card {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    padding: 1rem;
    box-shadow: var(--shadow-sm);
}

.report-card .title {
    font-weight: 600;
    color: var(--gray-800);
}

.report-card .meta {
    font-size: 0.8125rem;
    color: var(--gray-500);
    margin: 0.25rem 0 0.75rem;
}

.report-card .actions {
    display: flex;
    gap: 0.5rem;
}

.report-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 0.75rem;
}

.report-icon {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    color: white;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.125rem;
}

.report-info {
    flex: 1;
}

.report-title {
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 0.25rem;
}

.report-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.8125rem;
    color: var(--gray-500);
}

.report-id, .report-size, .report-date {
    display: flex;
    align-items: center;
}

.report-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.75rem;
}

/* Notifications */
.notifications-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.notification {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    padding: 1rem;
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    min-width: 300px;
    max-width: 400px;
    animation: slideIn 0.3s ease-out;
}

.notification.success {
    border-left: 4px solid var(--success-color);
}

.notification.error {
    border-left: 4px solid var(--danger-color);
}

.notification.warning {
    border-left: 4px solid var(--warning-color);
}

.notification.info {
    border-left: 4px solid var(--info-color);
}

.notification-close {
    background: none;
    border: none;
    color: var(--gray-400);
    cursor: pointer;
    padding: 0.25rem;
    margin-left: auto;
}

.notification-close:hover {
    color: var(--gray-600);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Settings */
.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1rem;
}

.setting-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    background: var(--gray-50);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
}

.setting-title {
    font-weight: 600;
    color: var(--gray-800);
}

.setting-desc {
    font-size: 0.875rem;
    color: var(--gray-600);
}

/* Logs */
.logs-container {
    background: #0b1020;
    color: #c9d1d9;
    font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
    font-size: 12px;
    min-height: 260px;
    max-height: 460px;
    overflow: auto;
    border-radius: var(--border-radius);
    border: 1px solid #1f2937;
    padding: 0.75rem;
}

.log-row { display: flex; gap: .5rem; align-items: baseline; padding: 2px 4px; }
.log-time { color: #9ca3af; width: 92px; flex: 0 0 auto; }
.log-level { font-weight: 700; width: 68px; flex: 0 0 auto; }
.log-msg { color: #e5e7eb; }
.log-level.INFO { color: #60a5fa; }
.log-level.WARN { color: #f59e0b; }
.log-level.ERROR { color: #ef4444; }
.log-level.DEBUG { color: #34d399; }

/* Responsive Design */
@media (max-width: 1200px) {
    :root {
        --sidebar-width: 260px;
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
    
    .quick-actions {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    }
}

@media (max-width: 1024px) {
    :root {
        --sidebar-width: 240px;
    }
    
    .toolbar {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }
    
    .search-box {
        max-width: none;
    }
    
    .filter-buttons {
        justify-content: center;
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    }
}

@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        left: -100%;
        z-index: 9999;
        transition: left 0.3s ease;
    }
    
    .sidebar.open {
        left: 0;
    }
    
    .main-wrapper {
        margin-left: 0;
    }
    
    .top-nav { padding: 0 1rem; grid-template-columns: 1fr auto; }
    
    .breadcrumb {
        display: none;
    }
    
    .main-content {
        padding: 1rem;
    }
    
    .page-title {
        font-size: 1.5rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .stat-card {
        padding: 1rem;
    }
    
    .quick-actions {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
    
    .toolbar-actions {
        flex-direction: column;
        width: 100%;
    }
    
    .filter-buttons {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-btn {
        justify-content: center;
    }
    
    .user-avatar span {
        display: none;
    }
}

@media (max-width: 480px) {
    .top-nav {
        padding: 0 0.75rem;
    }
    
    .main-content {
        padding: 0.75rem;
    }
    
    .page-header {
        margin-bottom: 1rem;
    }
    
    .dashboard-section,
    .filter-panel,
    .results-section {
        padding: 1rem;
    }
    
    .stat-card {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }
    
    .stat-icon {
        align-self: center;
    }
    
    .action-card {
        padding: 1rem;
    }
    
    .table th,
    .table td {
        padding: 0.5rem;
        font-size: 0.875rem;
    }
}

/* Mobile Menu Overlay */
@media (max-width: 768px) {
    .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 9998;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }
    
    .sidebar-overlay.show {
        opacity: 1;
        visibility: visible;
    }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: var(--gray-300);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gray-400);
}

/* Firefox Scrollbar */
* {
    scrollbar-width: thin;
    scrollbar-color: var(--gray-300) var(--gray-100);
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.content-section.active {
    animation: fadeIn 0.3s ease;
}

/* Focus styles for accessibility */
.nav-link:focus,
.btn:focus,
.filter-btn:focus,
.action-btn:focus,
.user-avatar:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}